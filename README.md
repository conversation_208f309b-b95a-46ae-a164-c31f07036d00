# Professional Photography Booking System Demo

## 🎯 Overview

This is a comprehensive demonstration of a professional photography booking system built specifically for photographers. The system streamlines the entire booking process from initial inquiry to confirmation, featuring automated email integration, WhatsApp messaging, and calendar synchronization.

## ✨ Key Features

### 📧 Email Automation
- Professional email templates with booking details
- Automatic confirmation emails to clients
- Calendar invites for all events
- Responsive email design for all devices

### 📱 WhatsApp Integration
- Instant WhatsApp messaging with pre-filled booking details
- Automated message generation based on event type
- Direct communication channel with clients

### 📅 Calendar Integration
- Automatic Google Calendar invite generation
- Event-specific calendar entries
- Multiple event support for combo packages

### 🎨 Professional UI/UX
- Modern, responsive design
- Multi-step booking form
- Real-time form validation
- Mobile-optimized interface

### 📊 Package Management
- Dynamic package selection based on event type
- Detailed package information display
- Pricing integration
- Custom package configurations

## 🌐 Bilingual Support

The system includes both English and Sinhala language support for value propositions and key messaging, making it accessible to a broader client base.

## 🛠 Technical Stack

- **Frontend**: React 18 with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **Build Tool**: Vite
- **Email Service**: EmailJS
- **PDF Generation**: jsPDF
- **Animations**: Framer Motion
- **Form Handling**: React Hook Form with Zod validation
- **Date Handling**: date-fns
- **Icons**: Lucide React

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/Laki117/BookingDemo.git
cd BookingDemo
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/45b09236-d961-40a8-8d5f-f4bd31bf622c) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
