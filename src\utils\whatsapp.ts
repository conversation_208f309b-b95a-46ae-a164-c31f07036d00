// WhatsApp integration utilities for Chalaka Photography booking system
// Phone number: +94763249526

const WHATSAPP_PHONE = "94763249526";

/**
 * Generates a unique booking reference number
 * Format: CDF-YYYYMMDD-XXX
 * CDF = Chalaka Dulanga Photography
 * YYYYMMDD = Current date
 * XXX = Random 3-digit number for uniqueness
 */
const generateBookingReference = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const dateStr = `${year}${month}${day}`;

  // Generate a random 3-digit number for uniqueness
  const randomNum = Math.floor(Math.random() * 900) + 100; // 100-999

  return `CDF-${dateStr}-${randomNum}`;
};

/**
 * Safely formats a value, handling undefined/null cases
 */
const safeValue = (value: any): string => {
  if (value === undefined || value === null || value === 'undefined' || value === 'null' || value === '') {
    return '';
  }
  return String(value);
};

/**
 * Formats a date string to a more readable format
 */
const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
};

/**
 * Formats a time string to 12-hour format
 */
const formatTime = (timeString: string): string => {
  if (!timeString) return '';
  const [hours, minutes] = timeString.split(':');
  const hour = parseInt(hours);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour % 12 || 12;
  return `${displayHour}:${minutes} ${ampm}`;
};

/**
 * Creates a section header for WhatsApp message
 */
const createSection = (title: string, content: string): string => {
  if (!content.trim()) return '';
  return `\n📋 *${title}*\n${content}\n`;
};

/**
 * Creates an info line for WhatsApp message
 */
const createInfoLine = (label: string, value: string): string => {
  const cleanValue = safeValue(value);
  if (!cleanValue) return '';
  return `• ${label}: ${cleanValue}\n`;
};

/**
 * Generates WhatsApp message for engagement bookings
 */
const generateEngagementMessage = (formData: any, bookingRef?: string): string => {
  const eventLabel = 'Engagement Session';
  const reference = bookingRef || generateBookingReference();

  let message = `🎉 *NEW BOOKING INQUIRY*\n`;
  message += `📸 *CHALAKA DULANGA PHOTOGRAPHY*\n`;
  message += `📋 *Reference:* ${reference}\n\n`;

  // Basic Information
  let basicInfo = '';
  basicInfo += createInfoLine('Event Type', eventLabel);
  basicInfo += createInfoLine("Bride's Name", formData.brideAName);
  basicInfo += createInfoLine("Groom's Name", formData.groomName);
  basicInfo += createInfoLine('Phone Number', formData.phoneNumber);
  basicInfo += createInfoLine('Email', formData.email);

  if (basicInfo) {
    message += createSection('BOOKING SUMMARY', basicInfo);
  }

  // Engagement Details
  let engagementDetails = '';
  engagementDetails += createInfoLine('Engagement Date', formatDate(formData.eventDate));
  engagementDetails += createInfoLine('Package Selected', formData.package);
  engagementDetails += createInfoLine('Venue / Hotel', formData.venue);
  engagementDetails += createInfoLine('Registration Time', formatTime(formData.registrationTime));
  engagementDetails += createInfoLine('Ring Exchange Time', formatTime(formData.ringExchangeTime));
  engagementDetails += createInfoLine('Expected Guests', formData.guestCount);
  engagementDetails += createInfoLine('Makeup Artist', formData.makeupArtist);

  if (engagementDetails) {
    message += createSection('ENGAGEMENT DETAILS', engagementDetails);
  }

  // Additional Notes
  if (formData.additionalNotes) {
    message += createSection('ADDITIONAL NOTES', formData.additionalNotes);
  }

  // How they heard about us
  if (formData.hearAbout) {
    message += createSection('HOW THEY HEARD ABOUT US', formData.hearAbout);
  }

  // Footer
  message += `\n📅 *Submitted:* ${new Date().toLocaleString()}\n`;
  message += `\n💼 Please respond within 24-48 hours to maintain client satisfaction.`;

  return message;
};

/**
 * Generates WhatsApp message for wedding bookings
 */
const generateWeddingMessage = (formData: any, bookingRef?: string): string => {
  const eventLabel = 'Wedding Day';
  const reference = bookingRef || generateBookingReference();

  let message = `🎉 *NEW BOOKING INQUIRY*\n`;
  message += `📸 *CHALAKA DULANGA PHOTOGRAPHY*\n`;
  message += `📋 *Reference:* ${reference}\n\n`;

  // Basic Information
  let basicInfo = '';
  basicInfo += createInfoLine('Event Type', eventLabel);
  basicInfo += createInfoLine("Bride's Name", formData.brideAName);
  basicInfo += createInfoLine("Groom's Name", formData.groomName);
  basicInfo += createInfoLine('Phone Number', formData.phoneNumber);
  basicInfo += createInfoLine('Email', formData.email);

  if (basicInfo) {
    message += createSection('BOOKING SUMMARY', basicInfo);
  }

  // Wedding Details
  let weddingDetails = '';
  weddingDetails += createInfoLine('Wedding Date', formatDate(formData.eventDate));
  weddingDetails += createInfoLine('Package Selected', formData.package);
  weddingDetails += createInfoLine('Venue / Hotel', formData.venue);
  weddingDetails += createInfoLine('Ceremony Start Time', formatTime(formData.ceremonyTime));
  weddingDetails += createInfoLine('Event End Time', formatTime(formData.eventEndTime));
  weddingDetails += createInfoLine('Primary Photoshoot Location', formData.primaryLocation);
  weddingDetails += createInfoLine('Expected Guests', formData.guestCount);
  weddingDetails += createInfoLine('Makeup Artist', formData.makeupArtist);

  if (weddingDetails) {
    message += createSection('WEDDING DETAILS', weddingDetails);
  }

  // Additional Notes
  if (formData.additionalNotes) {
    message += createSection('ADDITIONAL NOTES', formData.additionalNotes);
  }

  // How they heard about us
  if (formData.hearAbout) {
    message += createSection('HOW THEY HEARD ABOUT US', formData.hearAbout);
  }

  // Footer
  message += `\n📅 *Submitted:* ${new Date().toLocaleString()}\n`;
  message += `\n💼 Please respond within 24-48 hours to maintain client satisfaction.`;

  return message;
};

/**
 * Generates WhatsApp message for homecoming bookings
 */
const generateHomecomingMessage = (formData: any, bookingRef?: string): string => {
  const eventLabel = 'Homecoming Celebration';
  const reference = bookingRef || generateBookingReference();

  let message = `🎉 *NEW BOOKING INQUIRY*\n`;
  message += `📸 *CHALAKA DULANGA PHOTOGRAPHY*\n`;
  message += `📋 *Reference:* ${reference}\n\n`;

  // Basic Information
  let basicInfo = '';
  basicInfo += createInfoLine('Event Type', eventLabel);
  basicInfo += createInfoLine("Bride's Name", formData.brideAName);
  basicInfo += createInfoLine("Groom's Name", formData.groomName);
  basicInfo += createInfoLine('Phone Number', formData.phoneNumber);
  basicInfo += createInfoLine('Email', formData.email);

  if (basicInfo) {
    message += createSection('BOOKING SUMMARY', basicInfo);
  }

  // Homecoming Details
  let homecomingDetails = '';
  homecomingDetails += createInfoLine('Homecoming Date', formatDate(formData.homecomingDate));
  homecomingDetails += createInfoLine('Package Selected', formData.package);
  homecomingDetails += createInfoLine('Venue / Hotel', formData.venue);
  homecomingDetails += createInfoLine('Event End Time', formatTime(formData.eventEndTime));
  homecomingDetails += createInfoLine('Primary Photoshoot Location', formData.primaryLocation);
  homecomingDetails += createInfoLine('Expected Guests', formData.guestCount);
  homecomingDetails += createInfoLine('Makeup Artist', formData.makeupArtist);

  if (homecomingDetails) {
    message += createSection('HOMECOMING DETAILS', homecomingDetails);
  }

  // Additional Notes
  if (formData.additionalNotes) {
    message += createSection('ADDITIONAL NOTES', formData.additionalNotes);
  }

  // How they heard about us
  if (formData.hearAbout) {
    message += createSection('HOW THEY HEARD ABOUT US', formData.hearAbout);
  }

  // Footer
  message += `\n📅 *Submitted:* ${new Date().toLocaleString()}\n`;
  message += `\n💼 Please respond within 24-48 hours to maintain client satisfaction.`;

  return message;
};

/**
 * Generates WhatsApp message for wedding-homecoming combo bookings
 */
const generateWeddingHomecomingComboMessage = (formData: any, bookingRef?: string): string => {
  const eventLabel = 'Wedding & Homecoming Combo';
  const reference = bookingRef || generateBookingReference();

  let message = `🎉 *NEW BOOKING INQUIRY*\n`;
  message += `📸 *CHALAKA DULANGA PHOTOGRAPHY*\n`;
  message += `📋 *Reference:* ${reference}\n\n`;

  // Basic Information
  let basicInfo = '';
  basicInfo += createInfoLine('Event Type', eventLabel);
  basicInfo += createInfoLine("Bride's Name", formData.brideAName);
  basicInfo += createInfoLine("Groom's Name", formData.groomName);
  basicInfo += createInfoLine('Phone Number', formData.phoneNumber);
  basicInfo += createInfoLine('Email', formData.email);
  basicInfo += createInfoLine('Package Selected', formData.package);

  if (basicInfo) {
    message += createSection('BOOKING SUMMARY', basicInfo);
  }

  // Wedding Details
  let weddingDetails = '';
  weddingDetails += createInfoLine('Wedding Date', formatDate(formData.weddingDate));
  weddingDetails += createInfoLine('Venue / Hotel', formData.weddingVenue);
  weddingDetails += createInfoLine('Ceremony Start Time', formatTime(formData.ceremonyTime));
  weddingDetails += createInfoLine('Event End Time', formatTime(formData.eventEndTime));
  weddingDetails += createInfoLine('Expected Guests', formData.weddingGuestCount);

  if (weddingDetails) {
    message += createSection('WEDDING DETAILS', weddingDetails);
  }

  // Homecoming Details
  let homecomingDetails = '';
  homecomingDetails += createInfoLine('Homecoming Date', formatDate(formData.homecomingDate));
  homecomingDetails += createInfoLine('Venue / Hotel', formData.homecomingVenue);
  homecomingDetails += createInfoLine('Event End Time', formatTime(formData.homecomingEndTime));
  homecomingDetails += createInfoLine('Expected Guests', formData.homecomingGuestCount);

  if (homecomingDetails) {
    message += createSection('HOMECOMING DETAILS', homecomingDetails);
  }

  // Additional Information
  if (formData.makeupArtist) {
    message += createSection('MAKEUP ARTIST', formData.makeupArtist);
  }

  if (formData.additionalNotes) {
    message += createSection('ADDITIONAL NOTES', formData.additionalNotes);
  }

  // How they heard about us
  if (formData.hearAbout) {
    message += createSection('HOW THEY HEARD ABOUT US', formData.hearAbout);
  }

  // Footer
  message += `\n📅 *Submitted:* ${new Date().toLocaleString()}\n`;
  message += `\n💼 Please respond within 24-48 hours to maintain client satisfaction.`;

  return message;
};

/**
 * Generates WhatsApp message for triple combo bookings
 */
const generateTripleComboMessage = (formData: any, bookingRef?: string): string => {
  const eventLabel = 'Pre-shoot, Wedding & Homecoming Combo';
  const reference = bookingRef || generateBookingReference();

  let message = `🎉 *NEW BOOKING INQUIRY*\n`;
  message += `📸 *CHALAKA DULANGA PHOTOGRAPHY*\n`;
  message += `📋 *Reference:* ${reference}\n\n`;

  // Basic Information
  let basicInfo = '';
  basicInfo += createInfoLine('Event Type', eventLabel);
  basicInfo += createInfoLine("Bride's Name", formData.brideAName);
  basicInfo += createInfoLine("Groom's Name", formData.groomName);
  basicInfo += createInfoLine('Phone Number', formData.phoneNumber);
  basicInfo += createInfoLine('Email', formData.email);
  basicInfo += createInfoLine('Package Selected', formData.package);

  if (basicInfo) {
    message += createSection('BOOKING SUMMARY', basicInfo);
  }

  // Pre-Shoot Details
  let preShootDetails = '';
  preShootDetails += createInfoLine('Pre-Shoot Date', formatDate(formData.preShootDate));
  preShootDetails += createInfoLine('Number of Outfit Changes', formData.outfitChanges);
  preShootDetails += createInfoLine('Desired Location/Vibe', formData.desiredLocation);

  if (preShootDetails) {
    message += createSection('PRE-SHOOT DETAILS', preShootDetails);
  }

  // Wedding Details
  let weddingDetails = '';
  weddingDetails += createInfoLine('Wedding Date', formatDate(formData.weddingDate));
  weddingDetails += createInfoLine('Venue / Hotel', formData.weddingVenue);
  weddingDetails += createInfoLine('Ceremony Start Time', formatTime(formData.ceremonyTime));
  weddingDetails += createInfoLine('Event End Time', formatTime(formData.eventEndTime));
  weddingDetails += createInfoLine('Expected Guests', formData.weddingGuestCount);

  if (weddingDetails) {
    message += createSection('WEDDING DETAILS', weddingDetails);
  }

  // Homecoming Details
  let homecomingDetails = '';
  homecomingDetails += createInfoLine('Homecoming Date', formatDate(formData.homecomingDate));
  homecomingDetails += createInfoLine('Venue / Hotel', formData.homecomingVenue);
  homecomingDetails += createInfoLine('Event End Time', formatTime(formData.homecomingEndTime));
  homecomingDetails += createInfoLine('Expected Guests', formData.homecomingGuestCount);

  if (homecomingDetails) {
    message += createSection('HOMECOMING DETAILS', homecomingDetails);
  }

  // Additional Information
  if (formData.makeupArtist) {
    message += createSection('MAKEUP ARTIST', formData.makeupArtist);
  }

  if (formData.additionalNotes) {
    message += createSection('ADDITIONAL NOTES', formData.additionalNotes);
  }

  // How they heard about us
  if (formData.hearAbout) {
    message += createSection('HOW THEY HEARD ABOUT US', formData.hearAbout);
  }

  // Footer
  message += `\n📅 *Submitted:* ${new Date().toLocaleString()}\n`;
  message += `\n💼 Please respond within 24-48 hours to maintain client satisfaction.`;

  return message;
};

/**
 * Main function to generate WhatsApp message based on event type
 */
const generateWhatsAppMessage = (formData: any, bookingRef?: string): string => {
  const reference = bookingRef || generateBookingReference();

  switch (formData.eventType) {
    case 'engagement':
      return generateEngagementMessage(formData, reference);
    case 'wedding':
      return generateWeddingMessage(formData, reference);
    case 'homecoming':
      return generateHomecomingMessage(formData, reference);
    case 'wedding-homecoming':
      return generateWeddingHomecomingComboMessage(formData, reference);
    case 'triple-combo':
      return generateTripleComboMessage(formData, reference);
    default:
      return generateEngagementMessage(formData, reference); // fallback
  }
};

/**
 * Generates WhatsApp URL with encoded message
 */
const generateWhatsAppURL = (formData: any, bookingRef?: string): string => {
  const message = generateWhatsAppMessage(formData, bookingRef);
  const encodedMessage = encodeURIComponent(message);
  return `https://wa.me/${WHATSAPP_PHONE}?text=${encodedMessage}`;
};

export {
  WHATSAPP_PHONE,
  safeValue,
  formatDate,
  formatTime,
  createSection,
  createInfoLine,
  generateBookingReference,
  generateEngagementMessage,
  generateWeddingMessage,
  generateHomecomingMessage,
  generateWeddingHomecomingComboMessage,
  generateTripleComboMessage,
  generateWhatsAppMessage,
  generateWhatsAppURL
};
