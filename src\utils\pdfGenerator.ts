import jsPDF from 'jspdf';

/**
 * Generates a booking summary PDF for download
 */
export const generateBookingSummaryPDF = (formData: any, packageData: any, bookingReference: string) => {
  const doc = new jsPDF();
  
  // Set up fonts and colors
  const primaryColor = [44, 62, 80]; // Dark blue
  const secondaryColor = [52, 73, 94]; // Slightly lighter blue
  const textColor = [33, 37, 41]; // Dark gray
  
  let yPosition = 20;
  const pageWidth = doc.internal.pageSize.width;
  const margin = 20;
  const contentWidth = pageWidth - (margin * 2);
  
  // Helper function to add text with word wrapping
  const addWrappedText = (text: string, x: number, y: number, maxWidth: number, fontSize: number = 10) => {
    doc.setFontSize(fontSize);
    const lines = doc.splitTextToSize(text, maxWidth);
    doc.text(lines, x, y);
    return y + (lines.length * (fontSize * 0.4));
  };
  
  // Header
  doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.rect(0, 0, pageWidth, 40, 'F');
  
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(24);
  doc.setFont('helvetica', 'bold');
  doc.text('CHALAKA DULANGA', pageWidth / 2, 20, { align: 'center' });
  
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text('Photography & Cinematography', pageWidth / 2, 30, { align: 'center' });
  
  yPosition = 50;
  
  // Booking Reference
  doc.setFillColor(240, 240, 240);
  doc.rect(margin, yPosition, contentWidth, 15, 'F');
  doc.setTextColor(textColor[0], textColor[1], textColor[2]);
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text(`Booking Reference: ${bookingReference}`, pageWidth / 2, yPosition + 10, { align: 'center' });
  
  yPosition += 25;
  
  // Title
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text('BOOKING SUMMARY', pageWidth / 2, yPosition, { align: 'center' });
  
  yPosition += 15;
  
  // Event Type
  const eventOptions = {
    'engagement': 'Engagement Session',
    'wedding': 'Wedding Day',
    'homecoming': 'Homecoming Celebration',
    'wedding-homecoming': 'Wedding & Homecoming Combo',
    'triple-combo': 'Pre-shoot, Wedding & Homecoming Combo'
  };
  
  const eventLabel = eventOptions[formData.eventType] || formData.eventType;
  
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('Event Type:', margin, yPosition);
  doc.setFont('helvetica', 'normal');
  doc.text(eventLabel, margin + 40, yPosition);
  
  yPosition += 10;
  
  // Client Information Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('CLIENT INFORMATION', margin, yPosition);
  yPosition += 8;
  
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  
  if (formData.brideAName) {
    doc.text(`Bride's Name: ${formData.brideAName}`, margin, yPosition);
    yPosition += 6;
  }
  
  if (formData.groomName) {
    doc.text(`Groom's Name: ${formData.groomName}`, margin, yPosition);
    yPosition += 6;
  }
  
  if (formData.phoneNumber) {
    doc.text(`Phone: ${formData.phoneNumber}`, margin, yPosition);
    yPosition += 6;
  }
  
  if (formData.email) {
    doc.text(`Email: ${formData.email}`, margin, yPosition);
    yPosition += 6;
  }
  
  yPosition += 5;
  
  // Package Information
  if (formData.package) {
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('PACKAGE DETAILS', margin, yPosition);
    yPosition += 8;
    
    const packages = packageData[formData.eventType] || {};
    const packageInfo = packages[formData.package];
    
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text(`Selected Package: ${formData.package}`, margin, yPosition);
    yPosition += 8;
    
    if (packageInfo) {
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.text(`Price: ${packageInfo.price}`, margin, yPosition);
      yPosition += 8;
      
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.text('Package Includes:', margin, yPosition);
      yPosition += 6;
      
      doc.setFont('helvetica', 'normal');
      packageInfo.details.forEach((detail: string) => {
        yPosition = addWrappedText(`• ${detail}`, margin + 5, yPosition, contentWidth - 10, 9);
        yPosition += 2;
      });
    }
  }
  
  yPosition += 5;
  
  // Event Details Section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('EVENT DETAILS', margin, yPosition);
  yPosition += 8;
  
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  
  // Add event-specific details based on event type
  if (formData.eventType === 'engagement') {
    if (formData.eventDate) {
      doc.text(`Event Date: ${formData.eventDate}`, margin, yPosition);
      yPosition += 6;
    }
    if (formData.venue) {
      doc.text(`Venue: ${formData.venue}`, margin, yPosition);
      yPosition += 6;
    }
    if (formData.registrationTime) {
      doc.text(`Registration Time: ${formData.registrationTime}`, margin, yPosition);
      yPosition += 6;
    }
    if (formData.ringExchangeTime) {
      doc.text(`Ring Exchange Time: ${formData.ringExchangeTime}`, margin, yPosition);
      yPosition += 6;
    }
  }
  
  // Add more event details for other event types...
  if (formData.eventType === 'wedding') {
    if (formData.eventDate) {
      doc.text(`Wedding Date: ${formData.eventDate}`, margin, yPosition);
      yPosition += 6;
    }
    if (formData.venue) {
      doc.text(`Venue: ${formData.venue}`, margin, yPosition);
      yPosition += 6;
    }
    if (formData.ceremonyTime) {
      doc.text(`Ceremony Start: ${formData.ceremonyTime}`, margin, yPosition);
      yPosition += 6;
    }
    if (formData.primaryLocation) {
      doc.text(`Photoshoot Location: ${formData.primaryLocation}`, margin, yPosition);
      yPosition += 6;
    }
  }
  
  if (formData.guestCount) {
    doc.text(`Expected Guests: ${formData.guestCount}`, margin, yPosition);
    yPosition += 6;
  }
  
  if (formData.makeupArtist) {
    doc.text(`Makeup Artist: ${formData.makeupArtist}`, margin, yPosition);
    yPosition += 6;
  }
  
  if (formData.additionalNotes) {
    yPosition += 5;
    doc.setFont('helvetica', 'bold');
    doc.text('Additional Notes:', margin, yPosition);
    yPosition += 6;
    doc.setFont('helvetica', 'normal');
    yPosition = addWrappedText(formData.additionalNotes, margin, yPosition, contentWidth, 9);
  }
  
  // Check if we need a new page for terms
  if (yPosition > 220) {
    doc.addPage();
    yPosition = 20;
  } else {
    yPosition += 10;
  }
  
  // Terms and Conditions
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('TERMS AND CONDITIONS', margin, yPosition);
  yPosition += 8;
  
  const terms = [
    'A NON REFUNDABLE ADVANCE PAYMENT OF LKR 10,000 IS REQUIRED TO FREEZE THE DATE WITH US.',
    'BALANCE PAYMENT TO BE SETTLED 1 WEEK PRIOR TO THE WEDDING DAY.',
    'BANK TRANSFER OR YOU CAN PAY BY VISITING OUR LOCATION.',
    'ADDITIONAL HOURS RATE LKR 5000.',
    'TRAVELING CHARGES WILL BE ADDED.',
    'PLEASE NOTE THAT A LIMITED NUMBER OF WEDDING AND PRE WEDDING SESSION WILL BE PUBLISHED ON SOCIAL MEDIA ACCORDING TO THE PHOTOGRAPHER CHOICE.'
  ];
  
  doc.setFontSize(9);
  doc.setFont('helvetica', 'normal');
  
  terms.forEach((term, index) => {
    yPosition = addWrappedText(`${index + 1}. ${term}`, margin, yPosition, contentWidth, 9);
    yPosition += 3;
  });
  
  // Footer
  yPosition += 10;
  doc.setFontSize(8);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${new Date().toLocaleString()}`, margin, yPosition);
  doc.text('This is a booking inquiry summary and not a confirmed booking.', pageWidth / 2, yPosition + 5, { align: 'center' });
  
  // Download the PDF
  const fileName = `Chalaka_Photography_Booking_${bookingReference}.pdf`;
  doc.save(fileName);
};
