import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Check, Calendar, Clock, MapPin, Users, Camera, Heart, Star, Home, ChevronDown, ChevronUp, Info, MessageCircle, Download } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';
import emailjs from '@emailjs/browser';
import { generateWhatsAppURL, generateBookingReference } from '@/utils/whatsapp';
import { generateBookingSummaryPDF } from '@/utils/pdfGenerator';
import ValueProposition from '@/components/ValueProposition';

const Booking = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    eventType: '',
    brideAName: '',
    groomName: '',
    package: '',
    eventDate: '',
    venue: '',
    registrationTime: '',
    ringExchangeTime: '',
    ceremonyTime: '',
    eventEndTime: '',
    guestCount: '',
    makeupArtist: '',
    additionalNotes: '',
    weddingDate: '',
    homecomingDate: '',
    preShootDate: '',
    primaryLocation: '',
    desiredLocation: '',
    outfitChanges: '',
    phoneNumber: '',
    email: '',
    hearAbout: '',
    agreeTerms: false,
    // Fields for dual/triple combo packages
    weddingVenue: '',
    weddingGuestCount: '',
    homecomingVenue: '',
    homecomingGuestCount: '',
    homecomingEndTime: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPackageDetails, setShowPackageDetails] = useState(false);
  const [isTermsOpen, setIsTermsOpen] = useState(false);

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Auto-advance to step 2 when event type is selected in step 1
    if (field === 'eventType' && currentStep === 1 && value) {
      setTimeout(() => {
        setCurrentStep(2);
      }, 300); // Small delay for smooth transition
    }
  };

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const submitFormInternal = async (bookingReference?: string) => {
    if (!formData.agreeTerms) {
      toast({
        title: "Terms Required",
        description: "Please agree to the terms and conditions to proceed.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Helper function to ensure no undefined/null values
      const safeValue = (value) => {
        if (value === undefined || value === null || value === 'undefined' || value === 'null') {
          return '';
        }
        return String(value);
      };

      // Prepare email data to match EmailJS template structure exactly
      const emailData: any = {
        // Basic info
        event_plan: safeValue(eventOptions.find(e => e.value === formData.eventType)?.label || formData.eventType),
        bride_name: safeValue(formData.brideAName),
        groom_name: safeValue(formData.groomName),
        phone_number: safeValue(formData.phoneNumber),
        user_email: safeValue(formData.email),
        how_heard_about_us: safeValue(formData.hearAbout),

        // Boolean flags for template sections (EmailJS uses truthy/falsy values)
        is_engagement: formData.eventType === 'engagement',
        is_wedding: formData.eventType === 'wedding',
        is_homecoming: formData.eventType === 'homecoming',
        is_combo: formData.eventType === 'wedding-homecoming',
        is_triple_combo: formData.eventType === 'triple-combo',

        // Engagement specific fields
        engagement_package: formData.eventType === 'engagement' ? safeValue(formData.package) : '',
        engagement_date: formData.eventType === 'engagement' ? safeValue(formData.eventDate) : '',
        venue_hotel_engagement: formData.eventType === 'engagement' ? safeValue(formData.venue) : '',
        registration_time: safeValue(formData.registrationTime),
        ring_exchange_time: safeValue(formData.ringExchangeTime),
        event_end_time_engagement: formData.eventType === 'engagement' ? safeValue(formData.eventEndTime) : '',
        guest_count_engagement: formData.eventType === 'engagement' ? safeValue(formData.guestCount) : '',
        makeup_artist_engagement: formData.eventType === 'engagement' ? safeValue(formData.makeupArtist) : '',
        additional_notes_engagement: formData.eventType === 'engagement' ? safeValue(formData.additionalNotes) : '',

        // Wedding specific fields
        wedding_package: formData.eventType === 'wedding' ? safeValue(formData.package) : '',
        wedding_date: safeValue(formData.weddingDate),
        venue_hotel_wedding: formData.eventType === 'wedding' ? safeValue(formData.venue) : '',
        primary_photoshoot_location_wedding: formData.eventType === 'wedding' ? safeValue(formData.primaryLocation) : '',
        ceremony_start_time: safeValue(formData.ceremonyTime),
        event_end_time_wedding: formData.eventType === 'wedding' ? safeValue(formData.eventEndTime) : '',
        guest_count_wedding: formData.eventType === 'wedding' ? safeValue(formData.guestCount) : '',
        makeup_artist_wedding: formData.eventType === 'wedding' ? safeValue(formData.makeupArtist) : '',
        additional_notes_wedding: formData.eventType === 'wedding' ? safeValue(formData.additionalNotes) : '',

        // Homecoming specific fields
        homecoming_package: formData.eventType === 'homecoming' ? safeValue(formData.package) : '',
        homecoming_date: safeValue(formData.homecomingDate),
        venue_hotel_homecoming: formData.eventType === 'homecoming' ? safeValue(formData.venue) : '',
        primary_photoshoot_location_homecoming: formData.eventType === 'homecoming' ? safeValue(formData.primaryLocation) : '',
        event_end_time_homecoming: formData.eventType === 'homecoming' ? safeValue(formData.eventEndTime) : '',
        guest_count_homecoming: formData.eventType === 'homecoming' ? safeValue(formData.guestCount) : '',
        makeup_artist_homecoming: formData.eventType === 'homecoming' ? safeValue(formData.makeupArtist) : '',
        additional_notes_homecoming: formData.eventType === 'homecoming' ? safeValue(formData.additionalNotes) : '',

        // Dual Combo (Wedding + Homecoming) fields
        dual_package: formData.eventType === 'wedding-homecoming' ? safeValue(formData.package) : '',
        wedding_date_combo: formData.eventType === 'wedding-homecoming' ? safeValue(formData.weddingDate) : '',
        venue_hotel_combo_wedding: formData.eventType === 'wedding-homecoming' ? safeValue(formData.weddingVenue) : '',
        guest_count_combo_wedding: formData.eventType === 'wedding-homecoming' ? safeValue(formData.weddingGuestCount) : '',
        ceremony_start_time_combo: formData.eventType === 'wedding-homecoming' ? safeValue(formData.ceremonyTime) : '',
        event_end_time_combo_wedding: formData.eventType === 'wedding-homecoming' ? safeValue(formData.eventEndTime) : '',
        homecoming_date_combo: formData.eventType === 'wedding-homecoming' ? safeValue(formData.homecomingDate) : '',
        venue_hotel_combo_homecoming: formData.eventType === 'wedding-homecoming' ? safeValue(formData.homecomingVenue) : '',
        guest_count_combo_homecoming: formData.eventType === 'wedding-homecoming' ? safeValue(formData.homecomingGuestCount) : '',
        event_end_time_combo_homecoming: formData.eventType === 'wedding-homecoming' ? safeValue(formData.homecomingEndTime) : '',
        makeup_artist_combo: formData.eventType === 'wedding-homecoming' ? safeValue(formData.makeupArtist) : '',
        additional_notes_combo: formData.eventType === 'wedding-homecoming' ? safeValue(formData.additionalNotes) : '',

        // Triple Combo (Pre-shoot + Wedding + Homecoming) fields
        triple_package: formData.eventType === 'triple-combo' ? safeValue(formData.package) : '',
        pre_shoot_date: formData.eventType === 'triple-combo' ? safeValue(formData.preShootDate) : '',
        outfit_changes_pre_shoot: formData.eventType === 'triple-combo' ? safeValue(formData.outfitChanges) : '',
        pre_shoot_location_vibe: formData.eventType === 'triple-combo' ? safeValue(formData.desiredLocation) : '',
        wedding_date_triple: formData.eventType === 'triple-combo' ? safeValue(formData.weddingDate) : '',
        venue_hotel_triple_wedding: formData.eventType === 'triple-combo' ? safeValue(formData.weddingVenue) : '',
        guest_count_triple_wedding: formData.eventType === 'triple-combo' ? safeValue(formData.weddingGuestCount) : '',
        ceremony_start_time_triple: formData.eventType === 'triple-combo' ? safeValue(formData.ceremonyTime) : '',
        event_end_time_triple_wedding: formData.eventType === 'triple-combo' ? safeValue(formData.eventEndTime) : '',
        homecoming_date_triple: formData.eventType === 'triple-combo' ? safeValue(formData.homecomingDate) : '',
        venue_hotel_triple_homecoming: formData.eventType === 'triple-combo' ? safeValue(formData.homecomingVenue) : '',
        guest_count_triple_homecoming: formData.eventType === 'triple-combo' ? safeValue(formData.homecomingGuestCount) : '',
        event_end_time_triple_homecoming: formData.eventType === 'triple-combo' ? safeValue(formData.homecomingEndTime) : '',
        makeup_artist_triple: formData.eventType === 'triple-combo' ? safeValue(formData.makeupArtist) : '',
        additional_notes_triple: formData.eventType === 'triple-combo' ? safeValue(formData.additionalNotes) : ''
      };

      // Add a timestamp and booking reference for submission tracking
      emailData.submission_time = new Date().toLocaleString();
      emailData.booking_reference = bookingReference || 'Not provided';

      // Generate Google Calendar links for each event
      const generateCalendarLink = (eventTitle, eventDate, startTime, endTime, location, description) => {
        if (!eventDate) return '';

        // Format date for Google Calendar (YYYYMMDD)
        const formattedDate = eventDate.replace(/-/g, '');

        // Format times for Google Calendar (HHMMSS)
        const formatTime = (time) => {
          if (!time) return '090000'; // Default 9:00 AM
          return time.replace(':', '') + '00';
        };

        const startDateTime = formattedDate + 'T' + formatTime(startTime);
        const endDateTime = formattedDate + 'T' + formatTime(endTime || '18:00'); // Default 6:00 PM if no end time

        const params = new URLSearchParams({
          action: 'TEMPLATE',
          text: eventTitle,
          dates: `${startDateTime}/${endDateTime}`,
          details: description,
          location: location || ''
        });

        return `https://calendar.google.com/calendar/render?${params.toString()}`;
      };

      // Generate calendar links based on event type
      if (formData.eventType === 'engagement') {
        const engagementDescription = `Engagement photography session for ${formData.brideAName} & ${formData.groomName}
Package: ${formData.package}
Registration: ${formData.registrationTime}
Ring Exchange: ${formData.ringExchangeTime}
Venue: ${formData.venue}
Expected Guests: ${formData.guestCount}
Contact: ${formData.phoneNumber} | ${formData.email}
${formData.makeupArtist ? `Makeup Artist: ${formData.makeupArtist}` : ''}
${formData.additionalNotes ? `Notes: ${formData.additionalNotes}` : ''}`;

        emailData.calendar_link_engagement = generateCalendarLink(
          `${formData.brideAName} & ${formData.groomName} - Engagement Session`,
          formData.eventDate,
          formData.registrationTime,
          formData.eventEndTime,
          formData.venue,
          engagementDescription
        );
      }

      if (formData.eventType === 'wedding') {
        const weddingDescription = `Wedding photography for ${formData.brideAName} & ${formData.groomName}
Package: ${formData.package}
Ceremony Start: ${formData.ceremonyTime}
Event End: ${formData.eventEndTime}
Venue: ${formData.venue}
Photoshoot Location: ${formData.primaryLocation}
Expected Guests: ${formData.guestCount}
Contact: ${formData.phoneNumber} | ${formData.email}
${formData.makeupArtist ? `Makeup Artist: ${formData.makeupArtist}` : ''}
${formData.additionalNotes ? `Notes: ${formData.additionalNotes}` : ''}`;

        emailData.calendar_link_wedding = generateCalendarLink(
          `${formData.brideAName} & ${formData.groomName} - Wedding Day`,
          formData.weddingDate,
          formData.ceremonyTime,
          formData.eventEndTime,
          `${formData.venue}, ${formData.primaryLocation}`,
          weddingDescription
        );
      }

      if (formData.eventType === 'homecoming') {
        const homecomingDescription = `Homecoming photography for ${formData.brideAName} & ${formData.groomName}
Package: ${formData.package}
Event End: ${formData.eventEndTime}
Venue: ${formData.venue}
Photoshoot Location: ${formData.primaryLocation}
Expected Guests: ${formData.guestCount}
Contact: ${formData.phoneNumber} | ${formData.email}
${formData.makeupArtist ? `Makeup Artist: ${formData.makeupArtist}` : ''}
${formData.additionalNotes ? `Notes: ${formData.additionalNotes}` : ''}`;

        emailData.calendar_link_homecoming = generateCalendarLink(
          `${formData.brideAName} & ${formData.groomName} - Homecoming Celebration`,
          formData.homecomingDate,
          '10:00', // Default start time
          formData.eventEndTime,
          `${formData.venue}, ${formData.primaryLocation}`,
          homecomingDescription
        );
      }

      if (formData.eventType === 'wedding-homecoming') {
        const comboWeddingDescription = `Wedding photography (Dual Combo Package) for ${formData.brideAName} & ${formData.groomName}
Package: ${formData.package}
Ceremony Start: ${formData.ceremonyTime}
Event End: ${formData.eventEndTime}
Venue: ${formData.weddingVenue}
Expected Guests: ${formData.weddingGuestCount}
Contact: ${formData.phoneNumber} | ${formData.email}
${formData.makeupArtist ? `Makeup Artist: ${formData.makeupArtist}` : ''}
${formData.additionalNotes ? `Notes: ${formData.additionalNotes}` : ''}`;

        const comboHomecomingDescription = `Homecoming photography (Dual Combo Package) for ${formData.brideAName} & ${formData.groomName}
Package: ${formData.package}
Event End: ${formData.homecomingEndTime}
Venue: ${formData.homecomingVenue}
Expected Guests: ${formData.homecomingGuestCount}
Contact: ${formData.phoneNumber} | ${formData.email}
${formData.makeupArtist ? `Makeup Artist: ${formData.makeupArtist}` : ''}
${formData.additionalNotes ? `Notes: ${formData.additionalNotes}` : ''}`;

        emailData.calendar_link_combo_wedding = generateCalendarLink(
          `${formData.brideAName} & ${formData.groomName} - Wedding Day`,
          formData.weddingDate,
          formData.ceremonyTime,
          formData.eventEndTime,
          formData.weddingVenue,
          comboWeddingDescription
        );

        emailData.calendar_link_combo_homecoming = generateCalendarLink(
          `${formData.brideAName} & ${formData.groomName} - Homecoming Celebration`,
          formData.homecomingDate,
          '10:00', // Default start time
          formData.homecomingEndTime,
          formData.homecomingVenue,
          comboHomecomingDescription
        );
      }

      if (formData.eventType === 'triple-combo') {
        const triplePreShootDescription = `Pre-shoot photography session (Triple Combo Package) for ${formData.brideAName} & ${formData.groomName}
Package: ${formData.package}
Outfit Changes: ${formData.outfitChanges}
Desired Location/Vibe: ${formData.desiredLocation}
Contact: ${formData.phoneNumber} | ${formData.email}
${formData.makeupArtist ? `Makeup Artist: ${formData.makeupArtist}` : ''}
${formData.additionalNotes ? `Notes: ${formData.additionalNotes}` : ''}`;

        const tripleWeddingDescription = `Wedding photography (Triple Combo Package) for ${formData.brideAName} & ${formData.groomName}
Package: ${formData.package}
Ceremony Start: ${formData.ceremonyTime}
Event End: ${formData.eventEndTime}
Venue: ${formData.weddingVenue}
Expected Guests: ${formData.weddingGuestCount}
Contact: ${formData.phoneNumber} | ${formData.email}
${formData.makeupArtist ? `Makeup Artist: ${formData.makeupArtist}` : ''}
${formData.additionalNotes ? `Notes: ${formData.additionalNotes}` : ''}`;

        const tripleHomecomingDescription = `Homecoming photography (Triple Combo Package) for ${formData.brideAName} & ${formData.groomName}
Package: ${formData.package}
Event End: ${formData.homecomingEndTime}
Venue: ${formData.homecomingVenue}
Expected Guests: ${formData.homecomingGuestCount}
Contact: ${formData.phoneNumber} | ${formData.email}
${formData.makeupArtist ? `Makeup Artist: ${formData.makeupArtist}` : ''}
${formData.additionalNotes ? `Notes: ${formData.additionalNotes}` : ''}`;

        emailData.calendar_link_triple_preshoot = generateCalendarLink(
          `${formData.brideAName} & ${formData.groomName} - Pre-Shoot Session`,
          formData.preShootDate,
          '10:00', // Default pre-shoot time
          '16:00', // Default pre-shoot end
          formData.desiredLocation,
          triplePreShootDescription
        );

        emailData.calendar_link_triple_wedding = generateCalendarLink(
          `${formData.brideAName} & ${formData.groomName} - Wedding Day`,
          formData.weddingDate,
          formData.ceremonyTime,
          formData.eventEndTime,
          formData.weddingVenue,
          tripleWeddingDescription
        );

        emailData.calendar_link_triple_homecoming = generateCalendarLink(
          `${formData.brideAName} & ${formData.groomName} - Homecoming Celebration`,
          formData.homecomingDate,
          '10:00', // Default start time
          formData.homecomingEndTime,
          formData.homecomingVenue,
          tripleHomecomingDescription
        );
      }

      // Log email data for debugging (remove in production)
      console.log('Email data being sent:', emailData);
      console.log('Event type:', formData.eventType);
      console.log('Boolean flags:', {
        is_engagement: emailData.is_engagement,
        is_wedding: emailData.is_wedding,
        is_homecoming: emailData.is_homecoming,
        is_combo: emailData.is_combo,
        is_triple_combo: emailData.is_triple_combo
      });

      await emailjs.send(
        'service_lk610td',
        'template_7cm6mm7',
        emailData,
        'htHcoxPC-A-mGS62O'
      );

      // Email sent successfully - no redirect here as it's handled by the caller

    } catch (error) {
      console.error('EmailJS Error:', error);
      toast({
        title: "Error",
        description: "There was an issue sending your inquiry. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleWhatsAppSend = async () => {
    if (!formData.agreeTerms) {
      toast({
        title: "Terms Required",
        description: "Please agree to the terms and conditions to proceed.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate a unique booking reference for this inquiry
      const bookingReference = generateBookingReference();

      // First, submit the form via email with the booking reference
      await submitFormInternal(bookingReference);

      // Then open WhatsApp with the prefilled message including the same reference
      const whatsappURL = generateWhatsAppURL(formData, bookingReference);
      window.open(whatsappURL, '_blank');

      toast({
        title: "Inquiry Sent Successfully!",
        description: "Your booking inquiry has been submitted and WhatsApp opened with your details. Please send the WhatsApp message to complete your inquiry.",
        variant: "default"
      });

      // Redirect to success page after a short delay
      setTimeout(() => {
        navigate('/booking-success');
      }, 2000);

    } catch (error) {
      console.error('Submission Error:', error);
      toast({
        title: "Error",
        description: "There was an issue sending your inquiry. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDownloadSummary = () => {
    if (!formData.agreeTerms) {
      toast({
        title: "Terms Required",
        description: "Please agree to the terms and conditions to download the booking summary.",
        variant: "destructive"
      });
      return;
    }

    try {
      const bookingReference = generateBookingReference();
      generateBookingSummaryPDF(formData, packageData, bookingReference);

      toast({
        title: "Download Started",
        description: "Your booking summary is being downloaded.",
        variant: "default"
      });
    } catch (error) {
      console.error('PDF Generation Error:', error);
      toast({
        title: "Error",
        description: "There was an issue generating your booking summary. Please try again.",
        variant: "destructive"
      });
    }
  };

  const eventOptions = [
    { value: 'engagement', label: 'Engagement Session', icon: Heart },
    { value: 'wedding', label: 'Wedding Day', icon: Camera },
    { value: 'homecoming', label: 'Homecoming Celebration', icon: Star },
    { value: 'wedding-homecoming', label: 'Wedding & Homecoming Combo', icon: Camera },
    { value: 'triple-combo', label: 'Pre-shoot, Wedding & Homecoming Combo', icon: Camera }
  ];

  // Package data with prices and details
  const packageData = {
    engagement: {
      'Package 1': { price: '35,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', 'Thank You Card Design For Social Media', '30 High End Edited Photos', 'All Unedited Photos On Flash Drive'] },
      'Package 2': { price: '55,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', 'Thank You Card Design For Social Media', '1 Enlarged Photo (16"x24")', '50 Thank You Cards', '40 High End Edited Photos', 'Include Drone Photos', 'All Unedited Photos And Videos On Flash Drive'] },
      'Package 3': { price: '75,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', '30 Pages Story Album (8"x24")', '2 Enlarged Photo (16"x24")', '100 Thank You Cards', '50 High End Edited Photos', 'Include Drone Photos', 'Photo Slideshow Video (Main Photo Session)', 'Drone Video Trailer', 'All Unedited Photos And Videos On Flash Drive'] }
    },
    wedding: {
      'Package 1': { price: '60,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', 'Thank You Card Design For Social Media', '30 High End Edited Photos', 'All Unedited Photos On Flash Drive'] },
      'Package 2': { price: '80,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', '1 Enlarged Photos (12"x18")', '50 Thank You Cards', '35 High End Edited Photos', 'All Unedited Photos On Flash Drive'] },
      'Package 3': { price: '100,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', '30 Pages Story Album (8"x24")', '1 Enlarged Photos (16"x24")', '80 Thank You Cards', '40 High End Edited Photos', 'All Unedited Photos On Flash Drive'] },
      'Package 4': { price: '130,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', '50 Pages Story Album (10"x30")', '2 Enlarged Photos (16"x24")', '100 Thank You Cards', '50 High End Edited Photos', 'All Unedited Photos On Flash Drive'] },
      'Package 5': { price: '160,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', '60 Pages Story Album (12"x30")', '2 Enlarged Photos (20"x30")', '1 Family Enlarged Photos (12"x18")', '150 Thank You Cards', '50 High End Edited Photos', 'All Unedited Photos On Flash Drive'] },
      'Package 6': { price: '190,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', '100 Pages Story Album (12"x36")', '2 Enlarged Photos (20"x30") Wooden Frame', '2 Family Enlarged Photos (12"x18")', '200 Thank You Cards', '100 High End Edited Photos', 'Include Drone Photos', 'Drone Video Trailer (Main Photo Session)', 'All Unedited Photos On Flash Drive'] }
    },
    homecoming: {
      'Package 1': { price: '30,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Thank You Card Design For Social Media', '30 High End Edited Photos', 'All Unedited Photos On Flash Drive'] },
      'Package 2': { price: '50,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', 'Thank You Card Design For Social Media', '40 High End Edited Photos', 'All Unedited Photos On Flash Drive'] },
      'Package 3': { price: '70,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', '1 Enlarged Photos (16"x24")', '50 Thank You Cards', '50 High End Edited Photos', 'Include Drone Photos', 'All Unedited Photos On Flash Drive'] },
      'Package 4': { price: '90,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', '2 Enlarged Photos (16"x24")', '100 Thank You Cards', '50 High End Edited Photos', 'Include Drone Photos', 'All Unedited Photos On Flash Drive'] },
      'Package 5': { price: '150,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', '50 Pages Story Album (12"x30")', '2 Enlarged Photos (20"x30") Wooden', '60 Thank You Cards', '50 High End Edited Photos', 'Include Drone Photos', 'All Unedited Photos On Flash Drive'] },
      'Package 6': { price: '200,000.00 LKR', details: ['Main Photo Session At Preferred Location', 'Entire Ceremony & Reception Coverage', '80 Pages Story Album (12"x36")', '2 Enlarged Photos (20"x30") Wooden', '2 Family Enlarged Photos (12"x18")', '150 Thank You Cards', '60 High End Edited Photos', 'Include Drone Photos', 'Drone Video Trailer (Main Photo Session)', 'All Unedited Photos On Flash Drive'] }
    },
    'wedding-homecoming': {
      'Dual Package 1': { price: '100,000.00 LKR', details: ['Wedding: Main Photo Session, Thank You Card Design, Entire Ceremony & Reception Coverage, 30 High End Edited Photos, All Unedited Photos', 'Home Coming: Main Photo Session, Entire Ceremony & Reception Coverage, Thank You Card Design, 30 High End Edited Photos, All Unedited Photos'] },
      'Dual Package 2': { price: '160,000.00 LKR', details: ['Wedding: Main Photo Session, Entire Ceremony & Reception Coverage, 50 Pages Story Album (10"x24"), 2 Enlarged Photos (16"x24"), 100 Thank You Cards, 40 High End Edited Photos', 'Home Coming: Main Photo Session, Entire Ceremony & Reception Coverage, 2 Enlarged Photos (16"x24"), 100 Thank You Cards, 40 High End Edited Photos'] },
      'Dual Package 3': { price: '200,000.00 LKR', details: ['Wedding: Main Photo Session, Entire Ceremony & Reception Coverage, 100 Pages Story Album (12"x36"), 2 Enlarged Photos (20"x30") Wooden, 150 Thank You Cards, 50 High End Edited Photos, Include Drone Photos, Drone Video Trailer', 'Home Coming: Main Photo Session, Entire Ceremony & Reception Coverage, 2 Enlarged Photos (20"x30") Wooden, 150 Thank You Cards, 50 High End Edited Photos, Include Drone Photos'] }
    },
    'triple-combo': {
      'Combo Package 1': { price: '150,000.00 LKR', details: ['Pre-shoot: 2 Costumes, 30 High End Edited Photos, Include Drone Photos, Photo Slideshow Video', 'Wedding: Main Photo Session, Entire Ceremony & Reception Coverage, 2 Enlarged Photos (16"x24"), 100 Thank You Cards, 30 High End Edited Photos', 'Home Coming: Main Photo Session, Entire Ceremony & Reception Coverage, 2 Enlarged Photos (16"x24"), 100 Thank You Cards, 30 High End Edited Photos'] },
      'Combo Package 2': { price: '200,000.00 LKR', details: ['Pre-shoot: 3 Costumes, 40 High End Edited Photos, Include Drone Photos, Photo Slideshow Video, Drone Video Trailer', 'Wedding: Main Photo Session, Entire Ceremony & Reception Coverage, 60 Pages Story Album (12"x30"), 2 Enlarged Photos (16"x24"), 100 Thank You Cards, 40 High End Edited Photos, Include Drone Photos', 'Home Coming: Main Photo Session, Entire Ceremony & Reception Coverage, 2 Enlarged Photos (16"x24"), 100 Thank You Cards, 40 High End Edited Photos, Include Drone Photos'] },
      'Combo Package 3': { price: '270,000.00 LKR', details: ['Pre-shoot: 30 Pages Story Album (8"x24"), 4 Costumes, 50 High End Edited Photos, Include Drone Photos, Photo Slideshow Video, Drone Video Trailer', 'Wedding: Main Photo Session, Entire Ceremony & Reception Coverage, 50 Pages Story Album (12"x36"), 2 Enlarged Photos (20"x30") Wooden, 2 Family Enlarged Photos (12"x18"), 150 Thank You Cards, 50 High End Edited Photos, Include Drone Photos, Drone Video Trailer', 'Home Coming: Main Photo Session, Entire Ceremony & Reception Coverage, 2 Enlarged Photos (20"x30") Wooden, 150 Thank You Cards, 50 High End Edited Photos, Include Drone Photos'] }
    }
  };

  const getPackageOptions = () => {
    const packages = packageData[formData.eventType] || {};
    return Object.keys(packages);
  };

  const getPackageWithPrice = (packageName: string) => {
    const packages = packageData[formData.eventType] || {};
    const packageInfo = packages[packageName];
    return packageInfo ? `${packageName} - ${packageInfo.price}` : packageName;
  };

  const getSelectedPackageDetails = () => {
    const packages = packageData[formData.eventType] || {};
    return packages[formData.package]?.details || [];
  };

  // Terms and Conditions
  const termsAndConditions = [
    'A NON REFUNDABLE ADVANCE PAYMENT OF LKR 10,000 IS REQUIRED TO FREEZE THE DATE WITH US.',
    'BALANCE PAYMENT TO BE SETTLED 1 WEEK PRIOR TO THE WEDDING DAY.',
    'BANK TRANSFER OR YOU CAN PAY BY VISITING OUR LOCATION.',
    'ADDITIONAL HOURS RATE LKR 5000.',
    'TRAVELING CHARGES WILL BE ADDED.',
    'PLEASE NOTE THAT A LIMITED NUMBER OF WEDDING AND PRE WEDDING SESSION WILL BE PUBLISHED ON SOCIAL MEDIA ACCORDING TO THE PHOTOGRAPHER CHOICE.'
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Header */}
      <div className="relative overflow-hidden luxury-gradient">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative container mx-auto px-3 sm:px-4 py-6 sm:py-8">
          {/* Demo Info Button */}
          <div className="absolute top-3 left-3 sm:top-4 sm:left-4">
            <Button
              onClick={() => window.open('https://github.com/Laki117/BookingDemo', '_blank')}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20 transition-colors duration-200 text-xs sm:text-sm px-2 sm:px-3"
            >
              <Home className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
              <span className="hidden xs:inline">Demo Info</span>
            </Button>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center pt-8 sm:pt-0"
          >
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white px-4">
              Booking Demo - Professional Photography System
            </h1>
          </motion.div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6">
          <div className="flex items-center justify-between max-w-2xl mx-auto">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="flex items-center">
                <motion.div
                  className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center transition-all duration-300 ${
                    currentStep >= step
                      ? 'bg-gradient-to-r from-black to-gray-700 text-white shadow-lg'
                      : 'bg-gray-200 text-gray-500'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {currentStep > step ? (
                    <Check className="h-3 w-3 sm:h-5 sm:w-5" />
                  ) : (
                    <span className="font-semibold text-xs sm:text-sm">{step}</span>
                  )}
                </motion.div>
                {step < 4 && (
                  <div
                    className={`h-1 w-8 sm:w-16 md:w-24 mx-1 sm:mx-2 transition-all duration-300 ${
                      currentStep > step ? 'bg-gradient-to-r from-black to-gray-700' : 'bg-gray-200'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between max-w-2xl mx-auto mt-2 text-xs sm:text-sm text-gray-600 px-1">
            <span className="text-center">Event Type</span>
            <span className="text-center">Details</span>
            <span className="text-center">Contact</span>
            <span className="text-center">Review</span>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="container mx-auto px-3 sm:px-4 py-6 sm:py-8">
        <div className="max-w-2xl mx-auto">
          <AnimatePresence mode="wait">
            {currentStep === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="premium-shadow border-0 glass-effect">
                  <CardContent className="p-4 sm:p-6 md:p-8">
                    <div className="text-center mb-6 sm:mb-8">
                      <h2 className="text-2xl sm:text-3xl font-bold text-gradient">
                        Select Your Event Plan
                      </h2>
                      <p className="text-gray-600 mt-2 text-sm sm:text-base px-2">Choose the photography package that suits your celebration</p>
                    </div>

                    <RadioGroup
                      value={formData.eventType}
                      onValueChange={(value) => updateFormData('eventType', value)}
                      className="space-y-3 sm:space-y-4"
                    >
                      {eventOptions.map((option) => {
                        const IconComponent = option.icon;
                        return (
                          <motion.div
                            key={option.value}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <Label
                              htmlFor={option.value}
                              className={`flex items-center space-x-3 sm:space-x-4 p-4 sm:p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                                formData.eventType === option.value
                                  ? 'border-black bg-gradient-to-r from-gray-50 to-gray-100 shadow-lg'
                                  : 'border-gray-200 hover:border-gray-400 hover:bg-gray-50/50'
                              }`}
                            >
                              <RadioGroupItem value={option.value} id={option.value} className="sr-only" />
                              <div className={`p-2 sm:p-3 rounded-full flex-shrink-0 ${
                                formData.eventType === option.value
                                  ? 'bg-gradient-to-r from-black to-gray-700 text-white'
                                  : 'bg-gray-100 text-gray-500'
                              }`}>
                                <IconComponent className="h-5 w-5 sm:h-6 sm:w-6" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <span className="text-base sm:text-lg font-semibold text-gray-800 leading-tight">{option.label}</span>
                              </div>
                              {formData.eventType === option.value && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="w-5 h-5 sm:w-6 sm:h-6 bg-gradient-to-r from-black to-gray-700 rounded-full flex items-center justify-center flex-shrink-0"
                                >
                                  <Check className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
                                </motion.div>
                              )}
                            </Label>
                          </motion.div>
                        );
                      })}
                    </RadioGroup>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {currentStep === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="premium-shadow border-0 glass-effect">
                  <CardContent className="p-4 sm:p-6 md:p-8">
                    <div className="text-center mb-6 sm:mb-8">
                      <h2 className="text-2xl sm:text-3xl font-bold text-gradient">Event Details</h2>
                      <p className="text-gray-600 mt-2 text-sm sm:text-base px-2">Tell us about your special day</p>
                    </div>

                    <div className="space-y-4 sm:space-y-6">
                      {/* Couple Names - Always shown */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="brideAName" className="text-sm font-semibold text-gray-700 mb-2 block">Bride's Name</Label>
                          <Input
                            id="brideAName"
                            value={formData.brideAName}
                            onChange={(e) => updateFormData('brideAName', e.target.value)}
                            className="border-gray-300 focus:border-black h-11"
                            placeholder="Enter bride's name"
                          />
                        </div>
                        <div>
                          <Label htmlFor="groomName" className="text-sm font-semibold text-gray-700 mb-2 block">Groom's Name</Label>
                          <Input
                            id="groomName"
                            value={formData.groomName}
                            onChange={(e) => updateFormData('groomName', e.target.value)}
                            className="border-gray-300 focus:border-black h-11"
                            placeholder="Enter groom's name"
                          />
                        </div>
                      </div>

                      {/* Package Selection */}
                      <div>
                        <Label className="text-sm font-semibold text-gray-700 mb-2 block">Package Selection</Label>
                        <Select
                          value={formData.package}
                          onValueChange={(value) => {
                            updateFormData('package', value);
                            setShowPackageDetails(true);
                          }}
                        >
                          <SelectTrigger className="border-gray-300 focus:border-black h-11">
                            <SelectValue placeholder="Choose your package" />
                          </SelectTrigger>
                          <SelectContent>
                            {getPackageOptions().map((pkg) => (
                              <SelectItem key={pkg} value={pkg}>
                                {getPackageWithPrice(pkg)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Package Details */}
                      {formData.package && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="bg-blue-50 p-4 sm:p-6 rounded-lg border border-blue-200"
                        >
                          <div className="flex items-center gap-2 mb-3">
                            <Info className="w-5 h-5 text-blue-600" />
                            <h4 className="font-semibold text-blue-800">Package Details</h4>
                          </div>
                          <div className="space-y-2">
                            {getSelectedPackageDetails().map((detail, index) => (
                              <div key={index} className="flex items-start gap-2">
                                <Check className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                                <span className="text-sm text-gray-700">{detail}</span>
                              </div>
                            ))}
                          </div>
                        </motion.div>
                      )}

                      {/* Conditional Fields Based on Event Type */}
                      {formData.eventType === 'engagement' && (
                        <>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="eventDate" className="text-sm font-semibold text-gray-700 mb-2 block">Engagement Date</Label>
                              <Input
                                id="eventDate"
                                type="date"
                                value={formData.eventDate}
                                onChange={(e) => updateFormData('eventDate', e.target.value)}
                                className="border-gray-300 focus:border-black h-11"
                              />
                            </div>
                            <div>
                              <Label htmlFor="registrationTime" className="text-sm font-semibold text-gray-700 mb-2 block">Registration Time</Label>
                              <Input
                                id="registrationTime"
                                type="time"
                                value={formData.registrationTime}
                                onChange={(e) => updateFormData('registrationTime', e.target.value)}
                                className="border-gray-300 focus:border-black h-11"
                              />
                            </div>
                          </div>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="ringExchangeTime" className="text-sm font-semibold text-gray-700 mb-2 block">Ring Exchange Time (Approx.)</Label>
                              <Input
                                id="ringExchangeTime"
                                type="time"
                                value={formData.ringExchangeTime}
                                onChange={(e) => updateFormData('ringExchangeTime', e.target.value)}
                                className="border-gray-300 focus:border-black h-11"
                              />
                            </div>
                            <div>
                              <Label htmlFor="eventEndTime" className="text-sm font-semibold text-gray-700 mb-2 block">Approx. Event End Time</Label>
                              <Input
                                id="eventEndTime"
                                type="time"
                                value={formData.eventEndTime}
                                onChange={(e) => updateFormData('eventEndTime', e.target.value)}
                                className="border-gray-300 focus:border-black h-11"
                              />
                            </div>
                          </div>
                        </>
                      )}

                      {formData.eventType === 'wedding' && (
                        <>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="weddingDate" className="text-sm font-semibold text-gray-700">Wedding Date</Label>
                              <Input
                                id="weddingDate"
                                type="date"
                                value={formData.weddingDate}
                                onChange={(e) => updateFormData('weddingDate', e.target.value)}
                                className="mt-1 border-gray-300 focus:border-black"
                              />
                            </div>
                            <div>
                              <Label htmlFor="ceremonyTime" className="text-sm font-semibold text-gray-700">Ceremony (Poruwa) Start Time</Label>
                              <Input
                                id="ceremonyTime"
                                type="time"
                                value={formData.ceremonyTime}
                                onChange={(e) => updateFormData('ceremonyTime', e.target.value)}
                                className="mt-1 border-gray-300 focus:border-black"
                              />
                            </div>
                          </div>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="eventEndTime" className="text-sm font-semibold text-gray-700 mb-2 block">Approx. Event End Time</Label>
                              <Input
                                id="eventEndTime"
                                type="time"
                                value={formData.eventEndTime}
                                onChange={(e) => updateFormData('eventEndTime', e.target.value)}
                                className="border-gray-300 focus:border-black h-11"
                              />
                            </div>
                            <div>
                              <Label htmlFor="primaryLocation" className="text-sm font-semibold text-gray-700 mb-2 block">Primary Photoshoot Location</Label>
                              <Input
                                id="primaryLocation"
                                value={formData.primaryLocation}
                                onChange={(e) => updateFormData('primaryLocation', e.target.value)}
                                className="border-gray-300 focus:border-black h-11"
                                placeholder="Enter primary photoshoot location"
                              />
                            </div>
                          </div>
                        </>
                      )}

                      {formData.eventType === 'homecoming' && (
                        <>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="homecomingDate" className="text-sm font-semibold text-gray-700 mb-2 block">Homecoming Date</Label>
                              <Input
                                id="homecomingDate"
                                type="date"
                                value={formData.homecomingDate}
                                onChange={(e) => updateFormData('homecomingDate', e.target.value)}
                                className="border-gray-300 focus:border-black h-11"
                              />
                            </div>
                            <div>
                              <Label htmlFor="primaryLocation" className="text-sm font-semibold text-gray-700 mb-2 block">Primary Photoshoot Location</Label>
                              <Input
                                id="primaryLocation"
                                value={formData.primaryLocation}
                                onChange={(e) => updateFormData('primaryLocation', e.target.value)}
                                className="border-gray-300 focus:border-black h-11"
                                placeholder="Enter primary photoshoot location"
                              />
                            </div>
                          </div>
                        </>
                      )}

                      {formData.eventType === 'wedding-homecoming' && (
                        <>
                          {/* Wedding Details */}
                          <div className="bg-gray-50 p-4 sm:p-6 rounded-lg">
                            <h4 className="font-semibold text-gray-800 mb-4">Wedding Day Details</h4>
                            <div className="space-y-4">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <Label htmlFor="weddingDate" className="text-sm font-semibold text-gray-700">Wedding Date</Label>
                                  <Input
                                    id="weddingDate"
                                    type="date"
                                    value={formData.weddingDate}
                                    onChange={(e) => updateFormData('weddingDate', e.target.value)}
                                    className="mt-1 border-gray-300 focus:border-black"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="ceremonyTime" className="text-sm font-semibold text-gray-700">Ceremony (Poruwa) Start Time</Label>
                                  <Input
                                    id="ceremonyTime"
                                    type="time"
                                    value={formData.ceremonyTime}
                                    onChange={(e) => updateFormData('ceremonyTime', e.target.value)}
                                    className="mt-1 border-gray-300 focus:border-black"
                                  />
                                </div>
                              </div>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                  <Label htmlFor="weddingVenue" className="text-sm font-semibold text-gray-700 mb-2 block">Wedding Venue / Hotel Name</Label>
                                  <Input
                                    id="weddingVenue"
                                    value={formData.weddingVenue}
                                    onChange={(e) => updateFormData('weddingVenue', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                    placeholder="Enter wedding venue name"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="weddingGuestCount" className="text-sm font-semibold text-gray-700 mb-2 block">Expected Wedding Guest Count</Label>
                                  <Input
                                    id="weddingGuestCount"
                                    type="number"
                                    value={formData.weddingGuestCount}
                                    onChange={(e) => updateFormData('weddingGuestCount', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                    placeholder="Enter expected guest count"
                                  />
                                </div>
                              </div>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                  <Label htmlFor="eventEndTime" className="text-sm font-semibold text-gray-700 mb-2 block">Wedding End Time</Label>
                                  <Input
                                    id="eventEndTime"
                                    type="time"
                                    value={formData.eventEndTime}
                                    onChange={(e) => updateFormData('eventEndTime', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="primaryLocation" className="text-sm font-semibold text-gray-700 mb-2 block">Wedding Photoshoot Location</Label>
                                  <Input
                                    id="primaryLocation"
                                    value={formData.primaryLocation}
                                    onChange={(e) => updateFormData('primaryLocation', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                    placeholder="Enter wedding photoshoot location"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Homecoming Details */}
                          <div className="bg-blue-50 p-4 rounded-lg">
                            <h4 className="font-semibold text-gray-800 mb-4">Homecoming Celebration Details</h4>
                            <div className="space-y-4">
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                  <Label htmlFor="homecomingDate" className="text-sm font-semibold text-gray-700 mb-2 block">Homecoming Date</Label>
                                  <Input
                                    id="homecomingDate"
                                    type="date"
                                    value={formData.homecomingDate}
                                    onChange={(e) => updateFormData('homecomingDate', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="homecomingVenue" className="text-sm font-semibold text-gray-700 mb-2 block">Homecoming Venue / Hotel Name</Label>
                                  <Input
                                    id="homecomingVenue"
                                    value={formData.homecomingVenue}
                                    onChange={(e) => updateFormData('homecomingVenue', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                    placeholder="Enter homecoming venue name"
                                  />
                                </div>
                              </div>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                  <Label htmlFor="homecomingGuestCount" className="text-sm font-semibold text-gray-700 mb-2 block">Expected Homecoming Guest Count</Label>
                                  <Input
                                    id="homecomingGuestCount"
                                    type="number"
                                    value={formData.homecomingGuestCount}
                                    onChange={(e) => updateFormData('homecomingGuestCount', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                    placeholder="Enter expected guest count"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="homecomingEndTime" className="text-sm font-semibold text-gray-700 mb-2 block">Homecoming End Time</Label>
                                  <Input
                                    id="homecomingEndTime"
                                    type="time"
                                    value={formData.homecomingEndTime}
                                    onChange={(e) => updateFormData('homecomingEndTime', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </>
                      )}

                      {formData.eventType === 'triple-combo' && (
                        <>
                          {/* Pre-Shoot Details */}
                          <div className="bg-green-50 p-4 sm:p-6 rounded-lg">
                            <h4 className="font-semibold text-gray-800 mb-4">Pre-Shoot Session Details</h4>
                            <div className="space-y-4">
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                  <Label htmlFor="preShootDate" className="text-sm font-semibold text-gray-700 mb-2 block">Pre-Shoot Date</Label>
                                  <Input
                                    id="preShootDate"
                                    type="date"
                                    value={formData.preShootDate}
                                    onChange={(e) => updateFormData('preShootDate', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="outfitChanges" className="text-sm font-semibold text-gray-700 mb-2 block">Number of Outfit Changes</Label>
                                  <Input
                                    id="outfitChanges"
                                    type="number"
                                    value={formData.outfitChanges}
                                    onChange={(e) => updateFormData('outfitChanges', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                    placeholder="Enter number of outfit changes"
                                  />
                                </div>
                              </div>
                              <div>
                                <Label htmlFor="desiredLocation" className="text-sm font-semibold text-gray-700 mb-2 block">Desired Location(s) / Vibe</Label>
                                <Textarea
                                  id="desiredLocation"
                                  value={formData.desiredLocation}
                                  onChange={(e) => updateFormData('desiredLocation', e.target.value)}
                                  className="border-gray-300 focus:border-black min-h-[80px]"
                                  placeholder="Describe your desired locations or vibe for pre-shoot"
                                  rows={3}
                                />
                              </div>
                            </div>
                          </div>

                          {/* Wedding Details for Triple Combo */}
                          <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-semibold text-gray-800 mb-4">Wedding Day Details</h4>
                            <div className="space-y-4">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <Label htmlFor="weddingDate" className="text-sm font-semibold text-gray-700">Wedding Date</Label>
                                  <Input
                                    id="weddingDate"
                                    type="date"
                                    value={formData.weddingDate}
                                    onChange={(e) => updateFormData('weddingDate', e.target.value)}
                                    className="mt-1 border-gray-300 focus:border-black"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="ceremonyTime" className="text-sm font-semibold text-gray-700">Ceremony (Poruwa) Start Time</Label>
                                  <Input
                                    id="ceremonyTime"
                                    type="time"
                                    value={formData.ceremonyTime}
                                    onChange={(e) => updateFormData('ceremonyTime', e.target.value)}
                                    className="mt-1 border-gray-300 focus:border-black"
                                  />
                                </div>
                              </div>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                  <Label htmlFor="weddingVenue" className="text-sm font-semibold text-gray-700 mb-2 block">Wedding Venue / Hotel Name</Label>
                                  <Input
                                    id="weddingVenue"
                                    value={formData.weddingVenue}
                                    onChange={(e) => updateFormData('weddingVenue', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                    placeholder="Enter wedding venue name"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="weddingGuestCount" className="text-sm font-semibold text-gray-700 mb-2 block">Expected Wedding Guest Count</Label>
                                  <Input
                                    id="weddingGuestCount"
                                    type="number"
                                    value={formData.weddingGuestCount}
                                    onChange={(e) => updateFormData('weddingGuestCount', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                    placeholder="Enter expected guest count"
                                  />
                                </div>
                              </div>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                  <Label htmlFor="eventEndTime" className="text-sm font-semibold text-gray-700 mb-2 block">Wedding End Time</Label>
                                  <Input
                                    id="eventEndTime"
                                    type="time"
                                    value={formData.eventEndTime}
                                    onChange={(e) => updateFormData('eventEndTime', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="primaryLocation" className="text-sm font-semibold text-gray-700 mb-2 block">Wedding Photoshoot Location</Label>
                                  <Input
                                    id="primaryLocation"
                                    value={formData.primaryLocation}
                                    onChange={(e) => updateFormData('primaryLocation', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                    placeholder="Enter wedding photoshoot location"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Homecoming Details for Triple Combo */}
                          <div className="bg-blue-50 p-4 rounded-lg">
                            <h4 className="font-semibold text-gray-800 mb-4">Homecoming Celebration Details</h4>
                            <div className="space-y-4">
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                  <Label htmlFor="homecomingDate" className="text-sm font-semibold text-gray-700 mb-2 block">Homecoming Date</Label>
                                  <Input
                                    id="homecomingDate"
                                    type="date"
                                    value={formData.homecomingDate}
                                    onChange={(e) => updateFormData('homecomingDate', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="homecomingVenue" className="text-sm font-semibold text-gray-700 mb-2 block">Homecoming Venue / Hotel Name</Label>
                                  <Input
                                    id="homecomingVenue"
                                    value={formData.homecomingVenue}
                                    onChange={(e) => updateFormData('homecomingVenue', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                    placeholder="Enter homecoming venue name"
                                  />
                                </div>
                              </div>
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                  <Label htmlFor="homecomingGuestCount" className="text-sm font-semibold text-gray-700 mb-2 block">Expected Homecoming Guest Count</Label>
                                  <Input
                                    id="homecomingGuestCount"
                                    type="number"
                                    value={formData.homecomingGuestCount}
                                    onChange={(e) => updateFormData('homecomingGuestCount', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                    placeholder="Enter expected guest count"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="homecomingEndTime" className="text-sm font-semibold text-gray-700 mb-2 block">Homecoming End Time</Label>
                                  <Input
                                    id="homecomingEndTime"
                                    type="time"
                                    value={formData.homecomingEndTime}
                                    onChange={(e) => updateFormData('homecomingEndTime', e.target.value)}
                                    className="border-gray-300 focus:border-black h-11"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </>
                      )}

                      {/* Venue Fields - Conditional based on event type */}
                      {formData.eventType !== 'wedding-homecoming' && formData.eventType !== 'triple-combo' && (
                        <>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="venue" className="text-sm font-semibold text-gray-700 mb-2 block">Venue / Hotel Name</Label>
                              <Input
                                id="venue"
                                value={formData.venue}
                                onChange={(e) => updateFormData('venue', e.target.value)}
                                className="border-gray-300 focus:border-black h-11"
                                placeholder="Enter venue or hotel name"
                              />
                            </div>
                            <div>
                              <Label htmlFor="guestCount" className="text-sm font-semibold text-gray-700 mb-2 block">Expected Guest Count</Label>
                              <Input
                                id="guestCount"
                                type="number"
                                value={formData.guestCount}
                                onChange={(e) => updateFormData('guestCount', e.target.value)}
                                className="border-gray-300 focus:border-black h-11"
                                placeholder="Enter guest count"
                              />
                            </div>
                          </div>
                        </>
                      )}

                      {/* Common fields for all event types - Makeup Artist and Additional Notes */}
                      <div className="grid grid-cols-1 gap-4">
                        <div>
                          <Label htmlFor="makeupArtist" className="text-sm font-semibold text-gray-700 mb-2 block">
                            {formData.eventType === 'wedding' || formData.eventType === 'homecoming' || formData.eventType.includes('combo')
                              ? "Bride's Makeup Artist / Salon (Optional)"
                              : "Makeup Artist / Salon (Optional)"}
                          </Label>
                          <Input
                            id="makeupArtist"
                            value={formData.makeupArtist}
                            onChange={(e) => updateFormData('makeupArtist', e.target.value)}
                            className="border-gray-300 focus:border-black h-11"
                            placeholder="Enter makeup artist/salon name"
                          />
                        </div>
                        <div>
                          <Label htmlFor="additionalNotes" className="text-sm font-semibold text-gray-700 mb-2 block">Additional Requirements / Notes</Label>
                          <Textarea
                            id="additionalNotes"
                            value={formData.additionalNotes}
                            onChange={(e) => updateFormData('additionalNotes', e.target.value)}
                            className="border-gray-300 focus:border-black min-h-[100px]"
                            placeholder="Any special requirements or notes..."
                            rows={4}
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {currentStep === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="premium-shadow border-0 glass-effect">
                  <CardContent className="p-4 sm:p-6 md:p-8">
                    <div className="text-center mb-6 sm:mb-8">
                      <h2 className="text-2xl sm:text-3xl font-bold text-gradient">
                        Contact Information
                      </h2>
                      <p className="text-gray-600 mt-2 text-sm sm:text-base px-2">How can we reach you?</p>
                    </div>

                    <div className="space-y-4 sm:space-y-6">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="phoneNumber" className="text-sm font-semibold text-gray-700 mb-2 block">Primary Phone Number</Label>
                          <Input
                            id="phoneNumber"
                            type="tel"
                            value={formData.phoneNumber}
                            onChange={(e) => updateFormData('phoneNumber', e.target.value)}
                            className="border-gray-300 focus:border-black h-11"
                            placeholder="Enter your phone number"
                          />
                        </div>
                        <div>
                          <Label htmlFor="email" className="text-sm font-semibold text-gray-700 mb-2 block">Email Address</Label>
                          <Input
                            id="email"
                            type="email"
                            value={formData.email}
                            onChange={(e) => updateFormData('email', e.target.value)}
                            className="border-gray-300 focus:border-black h-11"
                            placeholder="Enter your email address"
                          />
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="hearAbout" className="text-sm font-semibold text-gray-700 mb-2 block">How did you hear about me? / Additional Notes</Label>
                        <Textarea
                          id="hearAbout"
                          value={formData.hearAbout}
                          onChange={(e) => updateFormData('hearAbout', e.target.value)}
                          className="border-gray-300 focus:border-black min-h-[100px]"
                          placeholder="Tell us how you heard about this booking demo or any additional notes..."
                          rows={4}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {currentStep === 4 && (
              <motion.div
                key="step4"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="premium-shadow border-0 glass-effect">
                  <CardContent className="p-4 sm:p-6 md:p-8">
                    <div className="text-center mb-6 sm:mb-8">
                      <h2 className="text-2xl sm:text-3xl font-bold text-gradient">
                        Review & Submit
                      </h2>
                      <p className="text-gray-600 mt-2 text-sm sm:text-base px-2">Please review your information before submitting</p>
                    </div>

                    <div className="space-y-6">
                      {/* Summary Cards */}
                      <div className="grid gap-4">
                        {/* Basic Event Info */}
                        <Card className="border-gray-300">
                          <CardContent className="p-4">
                            <h3 className="font-semibold text-gray-800 mb-2">Event Overview</h3>
                            <div className="text-sm space-y-1">
                              <p><span className="font-medium">Event Type:</span> {eventOptions.find(e => e.value === formData.eventType)?.label}</p>
                              <p><span className="font-medium">Couple:</span> {formData.brideAName} & {formData.groomName}</p>
                              <p><span className="font-medium">Package:</span> {getPackageWithPrice(formData.package)}</p>
                              {formData.guestCount && <p><span className="font-medium">Expected Guests:</span> {formData.guestCount}</p>}
                            </div>
                          </CardContent>
                        </Card>

                        {/* Package Details */}
                        {formData.package && (
                          <Card className="border-blue-200 bg-blue-50">
                            <CardContent className="p-4">
                              <div className="flex items-center gap-2 mb-3">
                                <Info className="w-5 h-5 text-blue-600" />
                                <h3 className="font-semibold text-blue-800">Package Details</h3>
                              </div>
                              <div className="space-y-2">
                                {getSelectedPackageDetails().map((detail, index) => (
                                  <div key={index} className="flex items-start gap-2">
                                    <Check className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                                    <span className="text-sm text-gray-700">{detail}</span>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        )}

                        {/* Engagement Details */}
                        {formData.eventType === 'engagement' && (
                          <Card className="border-gray-300">
                            <CardContent className="p-4">
                              <h3 className="font-semibold text-gray-800 mb-2">Engagement Session Details</h3>
                              <div className="text-sm space-y-1">
                                {formData.eventDate && <p><span className="font-medium">Date:</span> {formData.eventDate}</p>}
                                {formData.venue && <p><span className="font-medium">Venue:</span> {formData.venue}</p>}

                                {formData.registrationTime && <p><span className="font-medium">Registration Time:</span> {formData.registrationTime}</p>}
                                {formData.ringExchangeTime && <p><span className="font-medium">Ring Exchange Time:</span> {formData.ringExchangeTime}</p>}
                                {formData.eventEndTime && <p><span className="font-medium">Event End Time:</span> {formData.eventEndTime}</p>}
                                {formData.makeupArtist && <p><span className="font-medium">Makeup Artist:</span> {formData.makeupArtist}</p>}
                              </div>
                            </CardContent>
                          </Card>
                        )}

                        {/* Wedding Details */}
                        {(formData.eventType === 'wedding' || formData.eventType === 'wedding-homecoming' || formData.eventType === 'triple-combo') && (
                          <Card className="border-gray-300">
                            <CardContent className="p-4">
                              <h3 className="font-semibold text-gray-800 mb-2">Wedding Day Details</h3>
                              <div className="text-sm space-y-1">
                                {formData.weddingDate && <p><span className="font-medium">Wedding Date:</span> {formData.weddingDate}</p>}
                                {formData.eventType === 'wedding' && formData.venue && <p><span className="font-medium">Venue:</span> {formData.venue}</p>}
                                {formData.eventType !== 'wedding' && formData.weddingVenue && <p><span className="font-medium">Venue:</span> {formData.weddingVenue}</p>}
                                {formData.eventType === 'wedding' && formData.primaryLocation && <p><span className="font-medium">Photoshoot Location:</span> {formData.primaryLocation}</p>}
                                {formData.eventType !== 'wedding' && formData.weddingGuestCount && <p><span className="font-medium">Expected Guests:</span> {formData.weddingGuestCount}</p>}
                                {formData.ceremonyTime && <p><span className="font-medium">Ceremony Start Time:</span> {formData.ceremonyTime}</p>}
                                {formData.eventEndTime && <p><span className="font-medium">Event End Time:</span> {formData.eventEndTime}</p>}
                                {formData.makeupArtist && <p><span className="font-medium">Makeup Artist:</span> {formData.makeupArtist}</p>}
                              </div>
                            </CardContent>
                          </Card>
                        )}

                        {/* Homecoming Details */}
                        {(formData.eventType === 'homecoming' || formData.eventType === 'wedding-homecoming' || formData.eventType === 'triple-combo') && (
                          <Card className="border-gray-300">
                            <CardContent className="p-4">
                              <h3 className="font-semibold text-gray-800 mb-2">Homecoming Celebration Details</h3>
                              <div className="text-sm space-y-1">
                                {formData.homecomingDate && <p><span className="font-medium">Homecoming Date:</span> {formData.homecomingDate}</p>}
                                {formData.eventType === 'homecoming' && formData.venue && <p><span className="font-medium">Venue:</span> {formData.venue}</p>}
                                {formData.eventType === 'homecoming' && formData.primaryLocation && <p><span className="font-medium">Photoshoot Location:</span> {formData.primaryLocation}</p>}
                                {formData.homecomingEndTime && <p><span className="font-medium">Event End Time:</span> {formData.homecomingEndTime}</p>}
                                {formData.eventType === 'homecoming' && formData.makeupArtist && <p><span className="font-medium">Makeup Artist:</span> {formData.makeupArtist}</p>}
                              </div>
                            </CardContent>
                          </Card>
                        )}

                        {/* Pre-Shoot Details */}
                        {formData.eventType === 'triple-combo' && (
                          <Card className="border-gray-300">
                            <CardContent className="p-4">
                              <h3 className="font-semibold text-gray-800 mb-2">Pre-Shoot Session Details</h3>
                              <div className="text-sm space-y-1">
                                {formData.preShootDate && <p><span className="font-medium">Pre-Shoot Date:</span> {formData.preShootDate}</p>}
                                {formData.desiredLocation && <p><span className="font-medium">Desired Location/Vibe:</span> {formData.desiredLocation}</p>}
                                {formData.outfitChanges && <p><span className="font-medium">Outfit Changes:</span> {formData.outfitChanges}</p>}
                              </div>
                            </CardContent>
                          </Card>
                        )}

                        <Card className="border-gray-300">
                          <CardContent className="p-4">
                            <h3 className="font-semibold text-gray-800 mb-2">Contact Information</h3>
                            <div className="text-sm space-y-1">
                              <p><span className="font-medium">Phone:</span> {formData.phoneNumber}</p>
                              <p><span className="font-medium">Email:</span> {formData.email}</p>
                              {formData.hearAbout && <p><span className="font-medium">How they heard about us:</span> {formData.hearAbout}</p>}
                            </div>
                          </CardContent>
                        </Card>

                        {/* Additional Notes Card */}
                        {formData.additionalNotes && (
                          <Card className="border-gray-300">
                            <CardContent className="p-4">
                              <h3 className="font-semibold text-gray-800 mb-2">Additional Notes</h3>
                              <div className="text-sm">
                                <p>{formData.additionalNotes}</p>
                              </div>
                            </CardContent>
                          </Card>
                        )}
                      </div>

                      {/* Terms and Conditions */}
                      <div className="bg-gray-50 rounded-lg p-4">
                        <Button
                          variant="ghost"
                          onClick={() => setIsTermsOpen(!isTermsOpen)}
                          className="w-full justify-between p-0 h-auto text-left hover:bg-transparent mb-3"
                        >
                          <span className="text-sm font-medium text-gray-800">
                            View Terms & Conditions
                          </span>
                          {isTermsOpen ? (
                            <ChevronUp className="h-4 w-4 text-gray-600" />
                          ) : (
                            <ChevronDown className="h-4 w-4 text-gray-600" />
                          )}
                        </Button>

                        <AnimatePresence>
                          {isTermsOpen && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                              className="overflow-hidden"
                            >
                              <div className="bg-white rounded-md p-4 border border-gray-200 mb-4">
                                <h4 className="font-semibold text-gray-800 mb-3">Terms & Conditions</h4>
                                <ul className="space-y-2 text-sm text-gray-700">
                                  {termsAndConditions.map((term, index) => (
                                    <li key={index} className="flex items-start gap-2">
                                      <span className="text-gray-400 mt-1">•</span>
                                      <span>{term}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>

                        <div className="flex items-start space-x-2">
                          <Checkbox
                            id="terms"
                            checked={formData.agreeTerms}
                            onCheckedChange={(checked) => updateFormData('agreeTerms', checked)}
                            className="mt-1"
                          />
                          <Label htmlFor="terms" className="text-sm text-gray-700 leading-relaxed">
                            I agree to the booking terms & conditions above. I understand that this is an inquiry and not a confirmed booking until discussed and agreed upon.
                          </Label>
                        </div>
                      </div>

                      {/* Download Booking Summary Button */}
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="mb-4"
                      >
                        <Button
                          onClick={handleDownloadSummary}
                          disabled={!formData.agreeTerms}
                          variant="outline"
                          className="w-full border-2 border-blue-600 text-blue-600 hover:bg-blue-50 font-semibold py-4 text-lg shadow-lg disabled:opacity-50"
                        >
                          <Download className="mr-2 h-5 w-5" />
                          Download Booking Summary
                        </Button>
                      </motion.div>

                      {/* WhatsApp Submit Button */}
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Button
                          onClick={handleWhatsAppSend}
                          disabled={!formData.agreeTerms || isSubmitting}
                          className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold py-4 text-lg shadow-lg disabled:opacity-50"
                        >
                          {isSubmitting ? (
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                              className="w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-2"
                            />
                          ) : (
                            <MessageCircle className="mr-2 h-5 w-5" />
                          )}
                          {isSubmitting ? 'Sending Inquiry...' : 'Submit Inquiry via WhatsApp'}
                        </Button>
                      </motion.div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Navigation Buttons */}
          <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-0 mt-6 sm:mt-8">
            <Button
              onClick={prevStep}
              disabled={currentStep === 1}
              variant="outline"
              className="border-gray-300 text-gray-600 hover:bg-gray-50 disabled:opacity-50 h-11 order-2 sm:order-1"
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>

            {currentStep < 4 && (
              <Button
                onClick={nextStep}
                disabled={
                  (currentStep === 1 && !formData.eventType) ||
                  (currentStep === 2 && (!formData.brideAName || !formData.groomName || !formData.package)) ||
                  (currentStep === 3 && (!formData.phoneNumber || !formData.email))
                }
                className="bg-gradient-to-r from-black to-gray-700 hover:from-gray-800 hover:to-gray-900 text-white h-11 order-1 sm:order-2"
              >
                Next
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Value Proposition Section */}
      <ValueProposition />

      {/* Developer Credit Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 py-4">
        <div className="container mx-auto px-4 text-center">
          <p className="text-xs text-gray-500">
            Built by{' '}
            <a
              href="https://wa.me/94715768552"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-600 hover:text-gray-800 transition-colors duration-200"
            >
              Tera Works
            </a>
            {' '}• Professional Photography Booking System Demo
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Booking;
